import React, { useEffect, useState, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>,
  Button,
  Card,
  CircularProgress,
  Divider,
  Grid,
  Paper,
  Tab,
  Tabs,
  TextField,
  Typography,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import InfoIcon from '@mui/icons-material/Info';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import AutorenewIcon from '@mui/icons-material/Autorenew';
import PauseCircleOutlineIcon from '@mui/icons-material/PauseCircleOutline';
import api from '../../../utils/api';

// 导入DataTable组件
import DataTable from 'examples/Tables/DataTable';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';

// 添加自动旋转动画的样式
const autoRefreshStyles = `
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
`;

const DataConsistencyCheck = () => {
  // 添加自动旋转的样式到文档头
  React.useEffect(() => {
    const styleElement = document.createElement('style');
    styleElement.innerHTML = autoRefreshStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);
  const [selectedTab, setSelectedTab] = useState(0); // 默认显示所有数据看板
  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [month, setMonth] = useState((new Date().getMonth() + 1).toString());
  const [showInfo, setShowInfo] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState(30);
  const [lastRefreshTime, setLastRefreshTime] = useState(null);

  // 新增表结果状态
  const [addTableResult, setAddTableResult] = useState({
    monthlySumEqualsTotal: false,
    disposalAmountEquals: false,
    balanceEquals: false,
    inconsistentRows: [],
    summaryData: {
      monthlySum: 0,
      addAmount: 0,
      disposalDetailSum: 0,
      disposalAmount: 0,
      calculatedBalance: 0,
      actualBalance: 0,
    },
  });

  // 处置表结果状态
  const [disposalTableResult, setDisposalTableResult] = useState({
    amountEquals: false,
    inconsistentRows: [],
    totalAmount: 0,
    detailSum: 0,
    difference: 0,
  });

  // 新增金额检查结果状态
  const [addAmountCheckResult, setAddAmountCheckResult] = useState({
    isConsistent: false,
    inconsistentRows: [],
    summaryData: {
      addTableAmount: 0,
      nonLitigationAmount: 0,
      litigationAmount: 0,
      impairmentReserveAmount: 0,
    },
  });

  // 减少金额检查结果状态
  const [disposalAmountCheckResult, setDisposalAmountCheckResult] = useState({
    isConsistent: false,
    inconsistentRows: [],
    summaryData: {
      disposalTableAmount: 0,
      nonLitigationAmount: 0,
      litigationAmount: 0,
      impairmentReserveAmount: 0,
    },
  });

  // 债务变动一致性检查结果状态
  const [debtChangeConsistencyResult, setDebtChangeConsistencyResult] = useState({
    isConsistent: false,
    totalDifference: 0,
    maxDifference: 0,
    addTableAmount: 0,
    litigationAmount: 0,
    nonLitigationAmount: 0,
    impairmentAmount: 0,
    inconsistentItems: [],
  });

  // 所有数据看板结果状态
  const [dataSummaryResult, setDataSummaryResult] = useState({
    newAddTable: {
      initialBalance: 0, // 新增期初金额
      newAmount: 0,
      yearNewAmount: 0,
      disposalAmount: 0,
      yearDisposalAmount: 0,
      endBalance: 0,
    },
    litigationTable: {
      initialBalance: 0, // 新增期初金额
      newAmount: 0,
      yearNewAmount: 0,
      disposalAmount: 0,
      yearDisposalAmount: 0,
      endBalance: 0,
    },
    nonLitigationTable: {
      initialBalance: 0, // 新增期初金额
      newAmount: 0,
      yearNewAmount: 0,
      disposalAmount: 0,
      yearDisposalAmount: 0,
      endBalance: 0,
    },
    impairmentTable: {
      initialBalance: 0, // 新增期初金额
      newAmount: 0,
      yearNewAmount: 0,
      disposalAmount: 0,
      yearDisposalAmount: 0,
      endBalance: 0,
    },
  });

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
    setShowInfo(true);
    setError(null);
  };

  const handleCheck = useCallback(async () => {
    if (!year || !month) {
      setError('请输入年份和月份');
      return;
    }

    // 将年份和月份转换为数字类型，确保传递给后端的是数字
    const numericYear = parseInt(year, 10);
    const numericMonth = parseInt(month, 10);

    setLoading(true);
    setShowInfo(false);
    setError(null);

    try {
      if (selectedTab === 0) {
        // 所有数据看板
        try {
          const response = await api.get('/datamonitor/consistency/summary', {
            params: { year: numericYear, month: numericMonth },
          });

          // 从API响应中构建数据结构
          // 后端返回格式为 Map<String, Map<String, BigDecimal>>
          // 后端返回的数据结构是：
          // {
          //   "newAmount": { "新增表": 值, "诉讼表": 值, "非诉讼表": 值, "减值准备表": 值 },
          //   "yearNewAmount": { "新增表": 值, "诉讼表": 值, "非诉讼表": 值, "减值准备表": 值 },
          //   "disposedAmount": { "新增表": 值, "诉讼表": 值, "非诉讼表": 值, "减值准备表": 值 },
          //   "yearDisposedAmount": { "新增表": 值, "诉讼表": 值, "非诉讼表": 值, "减值准备表": 值 },
          //   "endingBalance": { "新增表": 值, "诉讼表": 值, "非诉讼表": 值, "减值准备表": 值 }
          // }

          // 添加对yearNewAmount和yearDisposedAmount数据的处理
          // 先打印后端返回的全部字段名，以便检查

          // 打印后端返回的数据结构，以便调试

          const result = {
            newAddTable: {
              initialBalance: response.data?.initialBalance?.['新增表'] || 0, // 添加年初余额数据
              newAmount: response.data?.newAmount?.['新增表'] || 0,
              yearNewAmount: response.data?.yearNewAmount?.['新增表'] || 0,
              disposalAmount: response.data?.disposedAmount?.['新增表'] || 0,
              yearDisposalAmount: response.data?.yearDisposedAmount?.['新增表'] || 0,
              endBalance: response.data?.endingBalance?.['新增表'] || 0,
            },
            litigationTable: {
              initialBalance: response.data?.initialBalance?.['诉讼表'] || 0, // 添加年初余额数据
              newAmount: response.data?.newAmount?.['诉讼表'] || 0,
              yearNewAmount: response.data?.yearNewAmount?.['诉讼表'] || 0,
              disposalAmount: response.data?.disposedAmount?.['诉讼表'] || 0,
              yearDisposalAmount: response.data?.yearDisposedAmount?.['诉讼表'] || 0,
              endBalance: response.data?.endingBalance?.['诉讼表'] || 0,
            },
            nonLitigationTable: {
              initialBalance: response.data?.initialBalance?.['非诉讼表'] || 0, // 添加年初余额数据
              newAmount: response.data?.newAmount?.['非诉讼表'] || 0,
              yearNewAmount: response.data?.yearNewAmount?.['非诉讼表'] || 0,
              disposalAmount: response.data?.disposedAmount?.['非诉讼表'] || 0,
              yearDisposalAmount: response.data?.yearDisposedAmount?.['非诉讼表'] || 0,
              endBalance: response.data?.endingBalance?.['非诉讼表'] || 0,
            },
            impairmentTable: {
              initialBalance: response.data?.initialBalance?.['减值准备表'] || 0, // 添加年初余额数据
              newAmount: response.data?.newAmount?.['减值准备表'] || 0,
              yearNewAmount: response.data?.yearNewAmount?.['减值准备表'] || 0,
              disposalAmount: response.data?.disposedAmount?.['减值准备表'] || 0,
              yearDisposalAmount: response.data?.yearDisposedAmount?.['减值准备表'] || 0,
              endBalance: response.data?.endingBalance?.['减值准备表'] || 0,
            },
          };

          setDataSummaryResult(result);
        } catch (error) {
          console.error('所有数据看板API尚未实现，使用模拟数据', error);

          // API未实现时使用模拟数据
          const mockResult = {
            newAddTable: {
              initialBalance: 20000000, // 添加年初余额模拟数据 (上一年12月期末余额)
              newAmount: 500000,
              yearNewAmount: 5000000,
              disposalAmount: 200000,
              yearDisposalAmount: 2000000,
              endBalance: 300000,
            },
            litigationTable: {
              initialBalance: 19000000, // 添加年初余额模拟数据
              newAmount: 480000,
              yearNewAmount: 4800000,
              disposalAmount: 180000,
              yearDisposalAmount: 1800000,
              endBalance: 300000,
            },
            nonLitigationTable: {
              initialBalance: 18500000, // 添加年初余额模拟数据
              newAmount: 480000,
              yearNewAmount: 4800000,
              disposalAmount: 180000,
              yearDisposalAmount: 1800000,
              endBalance: 300000,
            },
            impairmentTable: {
              initialBalance: 19500000, // 添加年初余额模拟数据
              newAmount: 520000,
              yearNewAmount: 5200000,
              disposalAmount: 220000,
              yearDisposalAmount: 2200000,
              endBalance: 300000,
            },
          };

          setDataSummaryResult(mockResult);
        }
      } else if (selectedTab === 1) {
        // 检查新增表数据 - 使用axios直接调用
        const response = await api.get('/datamonitor/check-add-table-consistency', {
          params: { year: numericYear, month: numericMonth },
          // baseURL removed - using relative path, // 显式设置baseURL避免路径问题
        });

        // 确保即使API返回了不完整的数据，也能正确处理
        const result = {
          monthlySumEqualsTotal: response.data.monthlySumEqualsTotal || false,
          disposalAmountEquals: response.data.disposalAmountEquals || false,
          balanceEquals: response.data.balanceEquals || false,
          inconsistentRows: response.data.inconsistentRows || [],
          summaryData: {
            monthlySum: response.data.summaryData?.monthlySum || 0,
            addAmount: response.data.summaryData?.addAmount || 0,
            disposalDetailSum: response.data.summaryData?.disposalDetailSum || 0,
            disposalAmount: response.data.summaryData?.disposalAmount || 0,
            calculatedBalance: response.data.summaryData?.calculatedBalance || 0,
            actualBalance: response.data.summaryData?.actualBalance || 0,
          },
        };

        // 打印综合数据以便调试

        setAddTableResult(result);
      } else if (selectedTab === 4) {
        // 检查债务变动一致性
        const response = await api.get('/datamonitor/check-debt-change-consistency', {
          params: { year: numericYear, month: numericMonth },
          // baseURL removed - using relative path,
        });

        const result = {
          isConsistent: response.data.consistent || false,
          totalDifference: response.data.totalDifference || 0,
          maxDifference: response.data.maxDifference || 0,
          addTableAmount: response.data.addTableAmount || 0,
          litigationAmount: response.data.litigationAmount || 0,
          nonLitigationAmount: response.data.nonLitigationAmount || 0,
          impairmentAmount: response.data.impairmentAmount || 0,
          inconsistentItems: response.data.inconsistentItems || [],
        };

        setDebtChangeConsistencyResult(result);
      } else if (selectedTab === 3) {
        // 检查处置表数据
        const response = await api.get('/datamonitor/check-disposal-table-consistency', {
          params: { year: numericYear, month: numericMonth },
          // baseURL removed - using relative path,
        });

        const result = {
          amountEquals: response.data.amountEquals || false,
          inconsistentRows: response.data.inconsistentRows || [],
          totalAmount: response.data.totalAmount || 0,
          detailSum: response.data.detailSum || 0,
          difference: response.data.difference || 0,
        };

        setDisposalTableResult(result);
      } else if (selectedTab === 2) {
        // 新增金额检查
        try {
          const response = await api.get('/datamonitor/check-add-amount-consistency', {
            params: { year: numericYear, month: numericMonth },
            // baseURL removed - using relative path,
          });

          const result = {
            isConsistent: response.data.isConsistent || false,
            inconsistentRows: response.data.inconsistentRows || [],
            summaryData: {
              addTableAmount: response.data.summaryData?.addTableAmount || 0,
              nonLitigationAmount: response.data.summaryData?.nonLitigationAmount || 0,
              litigationAmount: response.data.summaryData?.litigationAmount || 0,
              impairmentReserveAmount: response.data.summaryData?.impairmentReserveAmount || 0,
            },
          };

          setAddAmountCheckResult(result);
        } catch (error) {
          console.error('新增金额检查API尚未实现，使用模拟数据', error); // 这里需要后端实现对应的接口
          // 前端先模拟数据结构
          // API未实现时使用模拟数据
          const mockResult = {
            isConsistent: false,
            inconsistentRows: [
              {
                debtor: '债务人A',
                addTableAmount: 100000,
                nonLitigationAmount: 90000,
                litigationAmount: 90000,
                impairmentReserveAmount: 100000,
                difference: 10000,
              },
              {
                debtor: '债务人B',
                addTableAmount: 200000,
                nonLitigationAmount: 180000,
                litigationAmount: 180000,
                impairmentReserveAmount: 205000,
                difference: 20000,
              },
            ],
            summaryData: {
              addTableAmount: 1000000,
              nonLitigationAmount: 950000,
              litigationAmount: 950000,
              impairmentReserveAmount: 1050000,
            },
          };

          setAddAmountCheckResult(mockResult);
        }
      } else if (selectedTab === 3) {
        // 减少金额检查
        try {
          const response = await api.get('/datamonitor/check-disposal-amount-consistency', {
            params: { year, month },
            // baseURL removed - using relative path,
          });

          const result = {
            isConsistent: response.data.isConsistent || false,
            inconsistentRows: response.data.inconsistentRows || [],
            summaryData: {
              disposalTableAmount: response.data.summaryData?.disposalTableAmount || 0,
              nonLitigationAmount: response.data.summaryData?.nonLitigationAmount || 0,
              litigationAmount: response.data.summaryData?.litigationAmount || 0,
              impairmentReserveAmount: response.data.summaryData?.impairmentReserveAmount || 0,
            },
          };

          setDisposalAmountCheckResult(result);
        } catch (error) {
          console.error('减少金额检查API尚未实现，使用模拟数据', error);

          // API未实现时使用模拟数据
          const mockResult = {
            isConsistent: false,
            inconsistentRows: [
              {
                debtor: '债务人A',
                disposalTableAmount: 50000,
                nonLitigationAmount: 45000,
                litigationAmount: 45000,
                impairmentReserveAmount: 55000,
                difference: 5000,
              },
              {
                debtor: '债务人C',
                disposalTableAmount: 150000,
                nonLitigationAmount: 140000,
                litigationAmount: 140000,
                impairmentReserveAmount: 155000,
                difference: 10000,
              },
            ],
            summaryData: {
              disposalTableAmount: 500000,
              nonLitigationAmount: 470000,
              litigationAmount: 470000,
              impairmentReserveAmount: 520000,
            },
          };

          setDisposalAmountCheckResult(mockResult);
        }
      } else if (selectedTab === 5) {
        // 所有数据看板
        try {
          // 获取当前年月数据
          const currentResponse = await api.get('/datamonitor/consistency/summary', {
            params: { year: numericYear, month: numericMonth },
            // baseURL removed - using relative path,
          });

          // 上一年12月作为期初数据
          const prevYear = numericYear - 1;
          const initialResponse = await api
            .get('/datamonitor/consistency/summary', {
              params: { year: prevYear, month: 12 }, // 上一年12月
              // baseURL removed - using relative path,
            })
            .catch(error => {
              return { data: {} }; // 空数据作为默认值
            });

          // 定义获取期初余额的辅助函数（使用上一年12月的期末余额作为期初余额）
          const getInitialBalance = tableType => {
            return initialResponse.data?.endingBalance?.[tableType] || 0;
          };

          // 从响应中构建数据结构
          // 后端返回格式为 Map<String, Map<String, BigDecimal>>
          // 后端返回的数据结构是：
          // {
          //   "newAmount": { "新增表": 值, "诉诉表": 值, "非诉诉表": 值, "减值准备表": 值 },
          //   "disposedAmount": { "新增表": 值, "诉诉表": 值, "非诉诉表": 值, "减值准备表": 值 },
          //   "endingBalance": { "新增表": 值, "诉诉表": 值, "非诉诉表": 值, "减值准备表": 值 }
          // }

          // 组织最终的数据结构
          const result = {
            newAddTable: {
              initialBalance: getInitialBalance('新增表'), // 新增期初金额
              newAmount: currentResponse.data?.newAmount?.['新增表'] || 0,
              yearNewAmount: currentResponse.data?.yearNewAmount?.['新增表'] || 0,
              disposalAmount: currentResponse.data?.disposedAmount?.['新增表'] || 0,
              yearDisposalAmount: currentResponse.data?.yearDisposedAmount?.['新增表'] || 0,
              endBalance: currentResponse.data?.endingBalance?.['新增表'] || 0,
            },
            litigationTable: {
              initialBalance: getInitialBalance('诉诉表'), // 诉诉表期初金额
              newAmount: currentResponse.data?.newAmount?.['诉诉表'] || 0,
              yearNewAmount: currentResponse.data?.yearNewAmount?.['诉诉表'] || 0,
              disposalAmount: currentResponse.data?.disposedAmount?.['诉诉表'] || 0,
              yearDisposalAmount: currentResponse.data?.yearDisposedAmount?.['诉诉表'] || 0,
              endBalance: currentResponse.data?.endingBalance?.['诉诉表'] || 0,
            },
            nonLitigationTable: {
              initialBalance: getInitialBalance('非诉诉表'), // 非诉诉表期初金额
              newAmount: currentResponse.data?.newAmount?.['非诉诉表'] || 0,
              yearNewAmount: currentResponse.data?.yearNewAmount?.['非诉诉表'] || 0,
              disposalAmount: currentResponse.data?.disposedAmount?.['非诉诉表'] || 0,
              yearDisposalAmount: currentResponse.data?.yearDisposedAmount?.['非诉诉表'] || 0,
              endBalance: currentResponse.data?.endingBalance?.['非诉诉表'] || 0,
            },
            impairmentTable: {
              initialBalance: getInitialBalance('减值准备表'), // 减值准备表期初金额
              newAmount: currentResponse.data?.newAmount?.['减值准备表'] || 0,
              yearNewAmount: currentResponse.data?.yearNewAmount?.['减值准备表'] || 0,
              disposalAmount: currentResponse.data?.disposedAmount?.['减值准备表'] || 0,
              yearDisposalAmount: currentResponse.data?.yearDisposedAmount?.['减值准备表'] || 0,
              endBalance: currentResponse.data?.endingBalance?.['减值准备表'] || 0,
            },
          };

          setDataSummaryResult(result);
        } catch (error) {
          console.error('所有数据看板API尚未实现，使用模拟数据', error);

          // API未实现时使用模拟数据
          const mockResult = {
            newAddTable: {
              newAmount: 500000,
              disposalAmount: 200000,
              endBalance: 300000,
            },
            litigationTable: {
              newAmount: 480000,
              disposalAmount: 180000,
              endBalance: 300000,
            },
            nonLitigationTable: {
              newAmount: 480000,
              disposalAmount: 180000,
              endBalance: 300000,
            },
            impairmentTable: {
              newAmount: 520000,
              disposalAmount: 220000,
              endBalance: 300000,
            },
          };

          setDataSummaryResult(mockResult);
        }
      }
    } catch (error) {
      console.error('数据检查API调用错误:', error);
      setError('数据检查失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  }, [
    year,
    month,
    selectedTab,
    setError,
    setLoading,
    setShowInfo,
    setAddTableResult,
    setDisposalTableResult,
    setAddAmountCheckResult,
    setDisposalAmountCheckResult,
    setDebtChangeConsistencyResult,
    setDataSummaryResult,
  ]);

  // 切换自动刷新状态
  const toggleAutoRefresh = () => {
    setAutoRefresh(!autoRefresh);
  };

  // 更新刷新间隔
  const handleRefreshIntervalChange = event => {
    const value = parseInt(event.target.value, 10);
    if (!isNaN(value) && value > 0) {
      setRefreshInterval(value);
    }
  };

  // 添加自动刷新的useEffect钩子
  useEffect(() => {
    // 首次加载时获取数据
    if (year && month) {
      handleCheck();
    }

    let intervalId;
    // 如果启用了自动刷新，设置定时器
    if (autoRefresh && year && month) {
      intervalId = setInterval(() => {
        handleCheck();
        setLastRefreshTime(new Date().toLocaleTimeString());
      }, refreshInterval * 1000); // 将秒转换为毫秒
    }

    // 组件卸载或依赖项变化时清除定时器
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [autoRefresh, refreshInterval, year, month, handleCheck]);

  const handleReset = () => {
    setYear(new Date().getFullYear().toString());
    setMonth((new Date().getMonth() + 1).toString());
    setShowInfo(true);
    setError(null);
    setLastRefreshTime(null); // 重置最后刷新时间
  };

  // 格式化货币
  const formatCurrency = value => {
    return (
      value?.toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      }) || '0.00'
    );
  };

  // 准备汇总检查的DataTable数据
  const prepareSummaryTableData = () => {
    const summaryData = addTableResult.summaryData || {
      monthlySum: 0,
      addAmount: 0,
      disposalDetailSum: 0,
      disposalAmount: 0,
      calculatedBalance: 0,
      actualBalance: 0,
    };

    // 定义表头
    const columns = [
      { Header: '检查项目', accessor: 'item', width: '40%' },
      { Header: '左值', accessor: 'leftValue', width: '15%' },
      { Header: '右值', accessor: 'rightValue', width: '15%' },
      { Header: '差额', accessor: 'difference', width: '15%' },
      { Header: '结果', accessor: 'result', width: '15%' },
    ];

    // 定义行数据
    const rows = [
      {
        item: '月度数据(1-12月)求和 vs 新增金额',
        leftValue: formatCurrency(summaryData.monthlySum),
        rightValue: formatCurrency(summaryData.addAmount),
        difference: formatCurrency(Math.abs(summaryData.monthlySum - summaryData.addAmount)),
        result: addTableResult.monthlySumEqualsTotal ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} />
            <Typography color="success.main">相等</Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ErrorOutlineIcon color="error" sx={{ mr: 1 }} />
            <Typography color="error.main">不相等</Typography>
          </Box>
        ),
      },
      {
        item: '减少金额 vs 减少明细总和',
        leftValue: formatCurrency(summaryData.disposalAmount),
        rightValue: formatCurrency(summaryData.disposalDetailSum),
        difference: formatCurrency(
          Math.abs(summaryData.disposalAmount - summaryData.disposalDetailSum),
        ),
        result: addTableResult.disposalAmountEquals ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} />
            <Typography color="success.main">相等</Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ErrorOutlineIcon color="error" sx={{ mr: 1 }} />
            <Typography color="error.main">不相等</Typography>
          </Box>
        ),
      },
      {
        item: '新增金额-减少金额 vs 债权余额',
        leftValue: formatCurrency(summaryData.calculatedBalance),
        rightValue: formatCurrency(summaryData.actualBalance),
        difference: formatCurrency(
          Math.abs(summaryData.calculatedBalance - summaryData.actualBalance),
        ),
        result: addTableResult.balanceEquals ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} />
            <Typography color="success.main">相等</Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ErrorOutlineIcon color="error" sx={{ mr: 1 }} />
            <Typography color="error.main">不相等</Typography>
          </Box>
        ),
      },
    ];

    return { columns, rows };
  };

  // 准备不一致行的DataTable数据
  const prepareInconsistentRowsData = () => {
    // 定义表头
    const columns = [
      { Header: '债务人', accessor: 'debtor', width: '55%' },
      { Header: '左值', accessor: 'calculated', width: '15%' },
      { Header: '右值', accessor: 'actual', width: '15%' },
      { Header: '差异', accessor: 'difference', width: '15%' },
    ];

    // 定义行数据
    const rows = (addTableResult.inconsistentRows || []).map(row => ({
      debtor: row.debtor || '未知',
      item: row.item || '未知',
      calculated: formatCurrency(row.calculated || 0),
      actual: formatCurrency(row.actual || 0),
      difference: formatCurrency(Math.abs((row.calculated || 0) - (row.actual || 0))),
    }));

    return { columns, rows };
  };

  // 准备处置表数据的DataTable数据
  const prepareDisposalTableData = () => {
    // 定义表头
    const columns = [
      { Header: '检查项目', accessor: 'item', width: '40%' },
      { Header: '减少金额', accessor: 'leftValue', width: '15%' },
      { Header: '减少金额总和', accessor: 'rightValue', width: '15%' },
      { Header: '差额', accessor: 'difference', width: '15%' },
      { Header: '结果', accessor: 'result', width: '15%' },
    ];

    // 定义行数据
    const rows = [
      {
        item: '减少金额 vs 减少明细总和',
        leftValue: formatCurrency(disposalTableResult.totalAmount || 0),
        rightValue: formatCurrency(disposalTableResult.detailSum || 0),
        difference: formatCurrency(disposalTableResult.difference || 0),
        result: disposalTableResult.amountEquals ? (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleOutlineIcon color="success" sx={{ mr: 1 }} />
            <Typography color="success.main">相等</Typography>
          </Box>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ErrorOutlineIcon color="error" sx={{ mr: 1 }} />
            <Typography color="error.main">不相等</Typography>
          </Box>
        ),
      },
    ];

    return { columns, rows };
  };

  // 准备新增金额检查的DataTable数据
  const prepareAddAmountCheckTableData = () => {
    return {
      columns: [
        { Header: '检查项目', accessor: 'item', width: '50%' },
        { Header: '左值', accessor: 'leftValue', width: '20%' },
        { Header: '右值', accessor: 'rightValue', width: '20%' },
        { Header: '差额', accessor: 'difference', width: '10%' },
      ],
      rows: [
        {
          item: '月度数据(1-12月)求和 vs 新增金额',
          leftValue: formatCurrency(addAmountCheckResult.summaryData.addTableAmount),
          rightValue: formatCurrency(addAmountCheckResult.summaryData.nonLitigationAmount),
          difference: formatCurrency(
            addAmountCheckResult.summaryData.addTableAmount -
              addAmountCheckResult.summaryData.nonLitigationAmount,
          ),
        },
        {
          item: '非诉讼表金额 vs 诉讼表金额总和',
          leftValue: formatCurrency(addAmountCheckResult.summaryData.nonLitigationAmount),
          rightValue: formatCurrency(addAmountCheckResult.summaryData.litigationAmount),
          difference: formatCurrency(
            addAmountCheckResult.summaryData.nonLitigationAmount -
              addAmountCheckResult.summaryData.litigationAmount,
          ),
        },
        {
          item: '新增金额 vs 债权金额',
          leftValue: formatCurrency(addAmountCheckResult.summaryData.addTableAmount),
          rightValue: formatCurrency(addAmountCheckResult.summaryData.impairmentReserveAmount),
          difference: formatCurrency(
            addAmountCheckResult.summaryData.addTableAmount -
              addAmountCheckResult.summaryData.impairmentReserveAmount,
          ),
        },
      ],
    };
  };

  // 准备减少金额检查的DataTable数据
  const prepareDisposalAmountCheckTableData = () => {
    return {
      columns: [
        { Header: '检查项目', accessor: 'item', width: '50%' },
        { Header: '左值', accessor: 'leftValue', width: '20%' },
        { Header: '右值', accessor: 'rightValue', width: '20%' },
        { Header: '差额', accessor: 'difference', width: '10%' },
      ],
      rows: [
        {
          item: '处置表金额 vs 非诉讼表金额',
          leftValue: formatCurrency(disposalAmountCheckResult.summaryData.disposalTableAmount),
          rightValue: formatCurrency(disposalAmountCheckResult.summaryData.nonLitigationAmount),
          difference: formatCurrency(
            disposalAmountCheckResult.summaryData.disposalTableAmount -
              disposalAmountCheckResult.summaryData.nonLitigationAmount,
          ),
        },
        {
          item: '非诉讼表金额 vs 诉讼表金额',
          leftValue: formatCurrency(disposalAmountCheckResult.summaryData.nonLitigationAmount),
          rightValue: formatCurrency(disposalAmountCheckResult.summaryData.litigationAmount),
          difference: formatCurrency(
            disposalAmountCheckResult.summaryData.nonLitigationAmount -
              disposalAmountCheckResult.summaryData.litigationAmount,
          ),
        },
        {
          item: '减少金额 vs 减值准备表金额',
          leftValue: formatCurrency(disposalAmountCheckResult.summaryData.disposalTableAmount),
          rightValue: formatCurrency(disposalAmountCheckResult.summaryData.impairmentReserveAmount),
          difference: formatCurrency(
            disposalAmountCheckResult.summaryData.disposalTableAmount -
              disposalAmountCheckResult.summaryData.impairmentReserveAmount,
          ),
        },
      ],
    };
  };

  // 渲染新增表结果
  const renderAddTableResult = () => {
    const summaryTableData = prepareSummaryTableData();
    const inconsistentRowsData = prepareInconsistentRowsData();

    return (
      <Box sx={{ mt: 2 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <MDBox pt={3}>
                <DataTable
                  table={summaryTableData}
                  isSorted={false}
                  entriesPerPage={false}
                  showTotalEntries={false}
                  pagination={{ variant: 'gradient', color: 'info' }}
                  noEndBorder
                />
              </MDBox>
            </Card>
          </Grid>

          {inconsistentRowsData.rows.length > 0 && (
            <Grid item xs={12} sx={{ mt: 3 }}>
              <Card>
                <MDBox
                  mx={2}
                  mt={-3}
                  py={2}
                  px={2}
                  variant="gradient"
                  bgColor="error"
                  borderRadius="lg"
                  coloredShadow="error"
                >
                  <MDTypography variant="h6" color="white">
                    不一致记录({inconsistentRowsData.rows.length}条)
                  </MDTypography>
                </MDBox>
                <MDBox pt={3}>
                  <DataTable
                    table={inconsistentRowsData}
                    isSorted={false}
                    entriesPerPage={false}
                    showTotalEntries={false}
                    pagination={{ variant: 'gradient', color: 'error' }}
                    noEndBorder
                  />
                </MDBox>
              </Card>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // 渲染处置表结果
  const renderDisposalTableResult = () => {
    const tableData = prepareDisposalTableData();
    return (
      <Box>
        <Grid container spacing={3}>
          {/* 汇总检查卡片 */}
          <Grid item xs={12}>
            <Card>
              <MDBox pt={3} px={3}>
                <MDTypography variant="h6" fontWeight="medium">
                  处置表数据一致性检查结果
                </MDTypography>
                <Divider sx={{ my: 2 }} />
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Alert
                      icon={
                        disposalTableResult.amountEquals ? (
                          <CheckCircleOutlineIcon fontSize="inherit" />
                        ) : (
                          <ErrorOutlineIcon fontSize="inherit" />
                        )
                      }
                      severity={disposalTableResult.amountEquals ? 'success' : 'error'}
                    >
                      {disposalTableResult.amountEquals
                        ? '减少金额与减少金额明细总和相等'
                        : `减少金额与减少金额明细总和不相等，差额: ${formatCurrency(
                          disposalTableResult.difference,
                        )}`}
                    </Alert>
                  </Grid>
                </Grid>
              </MDBox>
              <MDBox px={3} pb={3}>
                <DataTable
                  table={tableData}
                  isSorted={false}
                  entriesPerPage={false}
                  showTotalEntries={false}
                  noEndBorder
                />
              </MDBox>
            </Card>
          </Grid>

          {/* 不一致行列表 */}
          {disposalTableResult.inconsistentRows.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <MDBox
                  mx={2}
                  mt={-3}
                  py={2}
                  px={2}
                  variant="gradient"
                  bgColor="error"
                  borderRadius="lg"
                  coloredShadow="error"
                >
                  <MDTypography variant="h6" color="white">
                    不一致记录({disposalTableResult.inconsistentRows.length}条)
                  </MDTypography>
                </MDBox>
                <MDBox pt={3}>
                  <DataTable
                    table={{
                      columns: [
                        { Header: '债务人', accessor: 'debtor', width: '55%' },
                        { Header: '减少金额', accessor: 'calculated', width: '15%' },
                        { Header: '减少金额明细总和', accessor: 'actual', width: '15%' },
                        { Header: '差异', accessor: 'difference', width: '15%' },
                      ],
                      rows: (disposalTableResult.inconsistentRows || []).map(row => ({
                        debtor: row.debtor || '未知',
                        calculated: formatCurrency(row.debtAmount || 0),
                        actual: formatCurrency(row.disposalSum || 0),
                        difference: formatCurrency(row.difference || 0),
                      })),
                    }}
                    isSorted={false}
                    entriesPerPage={{ defaultValue: 10, entries: [5, 10, 25, 50] }}
                    showTotalEntries={true}
                    pagination={{ variant: 'gradient', color: 'error' }}
                    noEndBorder
                  />
                </MDBox>
              </Card>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // 渲染新增金额检查结果
  const renderAddAmountCheckResult = () => {
    const tableData = prepareAddAmountCheckTableData();

    return (
      <Box>
        <Grid container spacing={3}>
          {/* 统计数据卡片 */}
          <Grid item xs={12}>
            <Card>
              <MDBox pt={3} px={3}>
                <MDTypography variant="h6" fontWeight="medium">
                  新增金额一致性检查结果
                </MDTypography>
                <Divider sx={{ my: 2 }} />
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Alert
                      icon={
                        addAmountCheckResult.isConsistent ? (
                          <CheckCircleOutlineIcon fontSize="inherit" />
                        ) : (
                          <ErrorOutlineIcon fontSize="inherit" />
                        )
                      }
                      severity={addAmountCheckResult.isConsistent ? 'success' : 'error'}
                    >
                      {addAmountCheckResult.isConsistent
                        ? '所有新增金额数据一致'
                        : `发现 ${addAmountCheckResult.inconsistentRows.length} 条不一致记录`}
                    </Alert>
                  </Grid>
                </Grid>
              </MDBox>
              <MDBox px={3} pb={3}>
                <DataTable
                  table={tableData}
                  isSorted={false}
                  entriesPerPage={false}
                  showTotalEntries={false}
                  noEndBorder
                />
              </MDBox>
            </Card>
          </Grid>

          {/* 不一致记录列表 */}
          {addAmountCheckResult.inconsistentRows.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <MDBox
                  mx={2}
                  mt={-3}
                  py={2}
                  px={2}
                  variant="gradient"
                  bgColor="error"
                  borderRadius="lg"
                  coloredShadow="error"
                >
                  <MDTypography variant="h6" color="white">
                    不一致记录({addAmountCheckResult.inconsistentRows.length}条)
                  </MDTypography>
                </MDBox>
                <MDBox pt={3}>
                  <DataTable
                    table={{
                      columns: [
                        { Header: '债务人', accessor: 'debtor', width: '25%' },
                        { Header: '新增表金额', accessor: 'addTableAmount', width: '15%' },
                        { Header: '非诉讼表金额', accessor: 'nonLitigationAmount', width: '15%' },
                        { Header: '诉讼表金额', accessor: 'litigationAmount', width: '15%' },
                        {
                          Header: '减值准备表金额',
                          accessor: 'impairmentReserveAmount',
                          width: '15%',
                        },
                        { Header: '差异', accessor: 'difference', width: '15%' },
                      ],
                      rows: (addAmountCheckResult.inconsistentRows || []).map(row => ({
                        debtor: row.debtor || '未知',
                        addTableAmount: formatCurrency(row.addTableAmount || 0),
                        nonLitigationAmount: formatCurrency(row.nonLitigationAmount || 0),
                        litigationAmount: formatCurrency(row.litigationAmount || 0),
                        impairmentReserveAmount: formatCurrency(row.impairmentReserveAmount || 0),
                        difference: formatCurrency(row.difference || 0),
                      })),
                    }}
                    isSorted={false}
                    entriesPerPage={{ defaultValue: 10, entries: [5, 10, 25, 50] }}
                    showTotalEntries={true}
                    pagination={{ variant: 'gradient', color: 'error' }}
                    noEndBorder
                  />
                </MDBox>
              </Card>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // 渲染减少金额检查结果
  const renderDisposalAmountCheckResult = () => {
    const tableData = prepareDisposalAmountCheckTableData();

    return (
      <Box>
        <Grid container spacing={3}>
          {/* 统计数据卡片 */}
          <Grid item xs={12}>
            <Card>
              <MDBox pt={3} px={3}>
                <MDTypography variant="h6" fontWeight="medium">
                  减少金额一致性检查结果
                </MDTypography>
                <Divider sx={{ my: 2 }} />
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Alert
                      icon={
                        disposalAmountCheckResult.isConsistent ? (
                          <CheckCircleOutlineIcon fontSize="inherit" />
                        ) : (
                          <ErrorOutlineIcon fontSize="inherit" />
                        )
                      }
                      severity={disposalAmountCheckResult.isConsistent ? 'success' : 'error'}
                    >
                      {disposalAmountCheckResult.isConsistent
                        ? '所有减少金额数据一致'
                        : `发现 ${disposalAmountCheckResult.inconsistentRows.length} 条不一致记录`}
                    </Alert>
                  </Grid>
                </Grid>
              </MDBox>
              <MDBox px={3} pb={3}>
                <DataTable
                  table={tableData}
                  isSorted={false}
                  entriesPerPage={false}
                  showTotalEntries={false}
                  noEndBorder
                />
              </MDBox>
            </Card>
          </Grid>

          {/* 不一致记录列表 */}
          {disposalAmountCheckResult.inconsistentRows.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <MDBox
                  mx={2}
                  mt={-3}
                  py={2}
                  px={2}
                  variant="gradient"
                  bgColor="error"
                  borderRadius="lg"
                  coloredShadow="error"
                >
                  <MDTypography variant="h6" color="white">
                    不一致记录({disposalAmountCheckResult.inconsistentRows.length}条)
                  </MDTypography>
                </MDBox>
                <MDBox pt={3}>
                  <DataTable
                    table={{
                      columns: [
                        { Header: '债务人', accessor: 'debtor', width: '25%' },
                        { Header: '处置表金额', accessor: 'disposalTableAmount', width: '15%' },
                        { Header: '非诉讼表金额', accessor: 'nonLitigationAmount', width: '15%' },
                        { Header: '诉讼表金额', accessor: 'litigationAmount', width: '15%' },
                        {
                          Header: '减值准备表金额',
                          accessor: 'impairmentReserveAmount',
                          width: '15%',
                        },
                        { Header: '差异', accessor: 'difference', width: '15%' },
                      ],
                      rows: (disposalAmountCheckResult.inconsistentRows || []).map(row => ({
                        debtor: row.debtor || '未知',
                        disposalTableAmount: formatCurrency(row.disposalTableAmount || 0),
                        nonLitigationAmount: formatCurrency(row.nonLitigationAmount || 0),
                        litigationAmount: formatCurrency(row.litigationAmount || 0),
                        impairmentReserveAmount: formatCurrency(row.impairmentReserveAmount || 0),
                        difference: formatCurrency(row.difference || 0),
                      })),
                    }}
                    isSorted={false}
                    entriesPerPage={{ defaultValue: 10, entries: [5, 10, 25, 50] }}
                    showTotalEntries={true}
                    pagination={{ variant: 'gradient', color: 'error' }}
                    noEndBorder
                  />
                </MDBox>
              </Card>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // 渲染债务变动一致性检查结果
  const renderDebtChangeConsistencyResult = () => {
    // 格式化金额显示
    const formatCurrency = amount => {
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
      }).format(amount);
    };

    // 准备汇总数据
    const getSummaryTableData = () => {
      return [
        {
          id: 1,
          name: '新增表金额',
          value: formatCurrency(debtChangeConsistencyResult.addTableAmount || 0),
        },
        {
          id: 2,
          name: '诉讼表金额',
          value: formatCurrency(debtChangeConsistencyResult.litigationAmount || 0),
        },
        {
          id: 3,
          name: '非诉讼表金额',
          value: formatCurrency(debtChangeConsistencyResult.nonLitigationAmount || 0),
        },
        {
          id: 4,
          name: '减值准备表金额',
          value: formatCurrency(debtChangeConsistencyResult.impairmentAmount || 0),
        },
        {
          id: 5,
          name: '最大差异',
          value: formatCurrency(debtChangeConsistencyResult.maxDifference || 0),
        },
      ];
    };

    return (
      <Box>
        <Grid container spacing={2}>
          {/* 一致性检查结果头部 */}
          <Grid item xs={12}>
            <Alert
              severity={debtChangeConsistencyResult.isConsistent ? 'success' : 'error'}
              icon={
                debtChangeConsistencyResult.isConsistent ? (
                  <CheckCircleOutlineIcon />
                ) : (
                  <ErrorOutlineIcon />
                )
              }
              sx={{ mb: 2 }}
            >
              <Typography variant="h6">
                债务变动一致性检查结果:
                {debtChangeConsistencyResult.isConsistent ? ' 一致' : ' 不一致'}
              </Typography>
            </Alert>
          </Grid>

          {/* 汇总数据表格 */}
          <Grid item xs={12} md={12}>
            <Card>
              <MDBox p={3}>
                <MDBox display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <MDTypography variant="h5">汇总数据</MDTypography>
                </MDBox>
                <DataTable
                  table={{
                    columns: [
                      { Header: '#', accessor: 'id', width: '5%' },
                      { Header: '项目', accessor: 'name', width: '60%' },
                      { Header: '金额', accessor: 'value', width: '35%' },
                    ],
                    rows: getSummaryTableData(),
                  }}
                  isSorted={false}
                  entriesPerPage={false}
                  showTotalEntries={false}
                  noEndBorder
                />
              </MDBox>
            </Card>
          </Grid>

          {/* 不一致记录详情 */}
          {!debtChangeConsistencyResult.isConsistent &&
            debtChangeConsistencyResult.inconsistentItems?.length > 0 && (
            <Grid item xs={12}>
              <Card>
                <MDBox p={3}>
                  <MDBox display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                    <MDTypography variant="h5">不一致记录</MDTypography>
                  </MDBox>
                  <DataTable
                    table={{
                      columns: [
                        { Header: '债务人/债权人', accessor: 'name', width: '20%' },
                        { Header: '新增表金额', accessor: 'addTableAmount', width: '15%' },
                        { Header: '诉讼表金额', accessor: 'litigationAmount', width: '15%' },
                        { Header: '非诉讼表金额', accessor: 'nonLitigationAmount', width: '15%' },
                        { Header: '减值准备表金额', accessor: 'impairmentAmount', width: '15%' },
                        { Header: '差异', accessor: 'difference', width: '15%' },
                      ],
                      rows: (debtChangeConsistencyResult.inconsistentItems || []).map(item => ({
                        name: item.name || '未知',
                        addTableAmount: formatCurrency(item.addTableAmount || 0),
                        litigationAmount: formatCurrency(item.litigationAmount || 0),
                        nonLitigationAmount: formatCurrency(item.nonLitigationAmount || 0),
                        impairmentAmount: formatCurrency(item.impairmentAmount || 0),
                        difference: formatCurrency(item.difference || 0),
                      })),
                    }}
                    isSorted={false}
                    entriesPerPage={{ defaultValue: 10, entries: [5, 10, 25, 50] }}
                    showTotalEntries={true}
                    pagination={{ variant: 'gradient', color: 'error' }}
                    noEndBorder
                  />
                </MDBox>
              </Card>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  };

  // 渲染所有数据看板结果
  const renderDataSummaryResult = () => {
    // 格式化金额显示
    const formatCurrency = amount => {
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY',
      }).format(amount);
    };

    return (
      <Box>
        <Grid container spacing={3}>
          {/* 数据汇总表格 */}
          <Grid item xs={12}>
            <Card>
              <MDBox
                mx={2}
                mt={-3}
                py={2}
                px={2}
                variant="gradient"
                bgColor="info"
                borderRadius="lg"
                coloredShadow="info"
              >
                <MDTypography variant="h6" color="white">
                  各表数据汇总
                </MDTypography>
              </MDBox>
              <MDBox pt={3}>
                <DataTable
                  table={{
                    columns: [
                      { Header: '表名', accessor: 'item', width: '12%' },
                      { Header: '年初余额', accessor: 'initialBalance', width: '12%' },
                      { Header: '当月新增金额', accessor: 'newAmount', width: '12%' },
                      { Header: '累计新增金额', accessor: 'yearNewAmount', width: '12%' },
                      { Header: '当月减少金额', accessor: 'disposalAmount', width: '12%' },
                      { Header: '累计减少金额', accessor: 'yearDisposalAmount', width: '12%' },
                      { Header: '期末余额', accessor: 'endBalance', width: '12%' },
                      { Header: '校对', accessor: 'verification', width: '16%' },
                    ],
                    rows: [
                      {
                        item: '新增表',
                        initialBalance: formatCurrency(
                          dataSummaryResult.newAddTable.initialBalance || 0,
                        ),
                        newAmount: formatCurrency(dataSummaryResult.newAddTable.newAmount),
                        yearNewAmount: formatCurrency(
                          dataSummaryResult.newAddTable.yearNewAmount || 0,
                        ),
                        // 当月减少金额和累计减少金额显示为"--"
                        disposalAmount: '--',
                        yearDisposalAmount: '--',
                        endBalance: formatCurrency(dataSummaryResult.newAddTable.endBalance),
                        verification: (() => {
                          // 比较新增表的新增金额与减值准备表的新增金额
                          const newTableNewAmount = dataSummaryResult.newAddTable.newAmount || 0;
                          const impairmentTableNewAmount =
                            dataSummaryResult.impairmentTable.newAmount || 0;

                          // 当月新增金额对比
                          const newAmountDiff = newTableNewAmount - impairmentTableNewAmount;

                          // 如果差异为0，显示无差异，否则显示差异数额并标红
                          if (Math.abs(newAmountDiff) < 0.01) {
                            return (
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: 'success.main',
                                }}
                              >
                                <CheckCircleOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="success.main" sx={{ fontSize: 'inherit' }}>
                                  无差异
                                </Typography>
                              </Box>
                            );
                          } else {
                            return (
                              <Box
                                sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}
                              >
                                <ErrorOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="error.main" sx={{ fontSize: 'inherit' }}>
                                  有差异 {formatCurrency(newAmountDiff)}
                                </Typography>
                              </Box>
                            );
                          }
                        })(),
                      },
                      {
                        item: '诉讼表',
                        initialBalance: formatCurrency(
                          dataSummaryResult.litigationTable.initialBalance || 0,
                        ),
                        newAmount: formatCurrency(dataSummaryResult.litigationTable.newAmount),
                        yearNewAmount: formatCurrency(
                          dataSummaryResult.litigationTable.yearNewAmount || 0,
                        ),
                        disposalAmount: formatCurrency(
                          dataSummaryResult.litigationTable.disposalAmount,
                        ),
                        yearDisposalAmount: formatCurrency(
                          dataSummaryResult.litigationTable.yearDisposalAmount || 0,
                        ),
                        endBalance: formatCurrency(dataSummaryResult.litigationTable.endBalance),
                        verification: (() => {
                          // 计算是否有差异: 期初金额 + 累计新增金额 - 累计减少金额 - 期末余额 = 0
                          const initialBalance =
                            dataSummaryResult.litigationTable.initialBalance || 0;
                          const yearNewAmount =
                            dataSummaryResult.litigationTable.yearNewAmount || 0;
                          const yearDisposalAmount =
                            dataSummaryResult.litigationTable.yearDisposalAmount || 0;
                          const endBalance = dataSummaryResult.litigationTable.endBalance || 0;

                          const diff =
                            initialBalance + yearNewAmount - yearDisposalAmount - endBalance;

                          // 使用绝对值小于0.01来判断是否为0（避免浮点数精度问题）
                          if (Math.abs(diff) < 0.01) {
                            return (
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: 'success.main',
                                }}
                              >
                                <CheckCircleOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="success.main" sx={{ fontSize: 'inherit' }}>
                                  无差异
                                </Typography>
                              </Box>
                            );
                          } else {
                            return (
                              <Box
                                sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}
                              >
                                <ErrorOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="error.main" sx={{ fontSize: 'inherit' }}>
                                  {formatCurrency(diff)}
                                </Typography>
                              </Box>
                            );
                          }
                        })(),
                      },
                      {
                        item: '非诉讼表',
                        initialBalance: formatCurrency(
                          dataSummaryResult.nonLitigationTable.initialBalance || 0,
                        ),
                        newAmount: formatCurrency(dataSummaryResult.nonLitigationTable.newAmount),
                        yearNewAmount: formatCurrency(
                          dataSummaryResult.nonLitigationTable.yearNewAmount || 0,
                        ),
                        disposalAmount: formatCurrency(
                          dataSummaryResult.nonLitigationTable.disposalAmount,
                        ),
                        yearDisposalAmount: formatCurrency(
                          dataSummaryResult.nonLitigationTable.yearDisposalAmount || 0,
                        ),
                        endBalance: formatCurrency(dataSummaryResult.nonLitigationTable.endBalance),
                        verification: (() => {
                          // 计算是否有差异: 期初金额 + 累计新增金额 - 累计减少金额 - 期末余额 = 0
                          const initialBalance =
                            dataSummaryResult.nonLitigationTable.initialBalance || 0;
                          const yearNewAmount =
                            dataSummaryResult.nonLitigationTable.yearNewAmount || 0;
                          const yearDisposalAmount =
                            dataSummaryResult.nonLitigationTable.yearDisposalAmount || 0;
                          const endBalance = dataSummaryResult.nonLitigationTable.endBalance || 0;

                          const diff =
                            initialBalance + yearNewAmount - yearDisposalAmount - endBalance;

                          // 使用绝对值小于0.01来判断是否为0（避免浮点数精度问题）
                          if (Math.abs(diff) < 0.01) {
                            return (
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: 'success.main',
                                }}
                              >
                                <CheckCircleOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="success.main" sx={{ fontSize: 'inherit' }}>
                                  无差异
                                </Typography>
                              </Box>
                            );
                          } else {
                            return (
                              <Box
                                sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}
                              >
                                <ErrorOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="error.main" sx={{ fontSize: 'inherit' }}>
                                  {formatCurrency(diff)}
                                </Typography>
                              </Box>
                            );
                          }
                        })(),
                      },
                      {
                        item: '处置表',
                        initialBalance: formatCurrency(0),
                        newAmount: '--',
                        yearNewAmount: '--',
                        disposalAmount: formatCurrency(
                          dataSummaryResult.impairmentTable.disposalAmount || 0,
                        ),
                        yearDisposalAmount: formatCurrency(
                          dataSummaryResult.impairmentTable.yearDisposalAmount || 0,
                        ),
                        endBalance: formatCurrency(0),
                        verification: (() => {
                          // 比较处置表的减少金额与减值准备表的减少金额
                          const disposalAmount =
                            dataSummaryResult.impairmentTable.disposalAmount || 0;
                          const impairmentDisposalAmount =
                            dataSummaryResult.impairmentTable.disposalAmount || 0;

                          // 当月减少金额对比
                          const disposalAmountDiff = disposalAmount - impairmentDisposalAmount;

                          // 如果差异为0，显示无差异，否则显示差异数额并标红
                          if (Math.abs(disposalAmountDiff) < 0.01) {
                            return (
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: 'success.main',
                                }}
                              >
                                <CheckCircleOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="success.main" sx={{ fontSize: 'inherit' }}>
                                  无差异
                                </Typography>
                              </Box>
                            );
                          } else {
                            return (
                              <Box
                                sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}
                              >
                                <ErrorOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="error.main" sx={{ fontSize: 'inherit' }}>
                                  有差异 {formatCurrency(disposalAmountDiff)}
                                </Typography>
                              </Box>
                            );
                          }
                        })(),
                      },
                      {
                        item: '减值准备表',
                        initialBalance: formatCurrency(
                          dataSummaryResult.impairmentTable.initialBalance || 0,
                        ),
                        newAmount: formatCurrency(dataSummaryResult.impairmentTable.newAmount),
                        yearNewAmount: formatCurrency(
                          dataSummaryResult.impairmentTable.yearNewAmount || 0,
                        ),
                        disposalAmount: formatCurrency(
                          dataSummaryResult.impairmentTable.disposalAmount,
                        ),
                        yearDisposalAmount: formatCurrency(
                          dataSummaryResult.impairmentTable.yearDisposalAmount || 0,
                        ),
                        endBalance: formatCurrency(dataSummaryResult.impairmentTable.endBalance),
                        verification: (() => {
                          // 计算是否有差异: 期初金额 + 累计新增金额 - 累计减少金额 - 期末余额 = 0
                          const initialBalance =
                            dataSummaryResult.impairmentTable.initialBalance || 0;
                          const yearNewAmount =
                            dataSummaryResult.impairmentTable.yearNewAmount || 0;
                          const yearDisposalAmount =
                            dataSummaryResult.impairmentTable.yearDisposalAmount || 0;
                          const endBalance = dataSummaryResult.impairmentTable.endBalance || 0;

                          const diff =
                            initialBalance + yearNewAmount - yearDisposalAmount - endBalance;

                          // 使用绝对值小于0.01来判断是否为0（避免浮点数精度问题）
                          if (Math.abs(diff) < 0.01) {
                            return (
                              <Box
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  color: 'success.main',
                                }}
                              >
                                <CheckCircleOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="success.main" sx={{ fontSize: 'inherit' }}>
                                  无差异
                                </Typography>
                              </Box>
                            );
                          } else {
                            return (
                              <Box
                                sx={{ display: 'flex', alignItems: 'center', color: 'error.main' }}
                              >
                                <ErrorOutlineIcon sx={{ mr: 1, fontSize: '1rem' }} />
                                <Typography color="error.main" sx={{ fontSize: 'inherit' }}>
                                  {formatCurrency(diff)}
                                </Typography>
                              </Box>
                            );
                          }
                        })(),
                      },
                    ],
                  }}
                  isSorted={false}
                  entriesPerPage={false}
                  showTotalEntries={false}
                  noEndBorder
                />
              </MDBox>
            </Card>
          </Grid>
        </Grid>
      </Box>
    );
  };

  return (
    <Box>
      {/* 检查选项卡 */}
      <Tabs
        value={selectedTab}
        onChange={handleTabChange}
        sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}
      >
        <Tab label="所有数据看板" />
        <Tab label="新增表数据检查" />
        <Tab label="处置表数据检查" />
        <Tab label="新增金额检查" />
        <Tab label="处置金额检查" />
        <Tab label="债务变动一致性" />
      </Tabs>

      {/* 查询表单 */}
      <Paper sx={{ p: 3, display: 'flex', alignItems: 'flex-start', mb: 2, flexWrap: 'wrap' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', width: '100%' }}>
          <TextField
            label="年份"
            value={year}
            onChange={e => setYear(e.target.value)}
            variant="outlined"
            size="small"
            sx={{ mr: 2, width: 100 }}
          />
          <TextField
            label="月份"
            value={month}
            onChange={e => setMonth(e.target.value)}
            variant="outlined"
            size="small"
            sx={{ mr: 2, width: 100 }}
          />
          <Button
            variant="contained"
            color="primary"
            startIcon={<SearchIcon />}
            onClick={handleCheck}
            sx={{ mr: 1, color: 'white' }}
            disabled={loading}
          >
            {loading ? (
              <>
                <CircularProgress size={24} color="inherit" sx={{ mr: 1 }} /> 检查中...
              </>
            ) : (
              <span style={{ color: '#ffffff' }}>开始检查</span>
            )}
          </Button>
          <Button
            variant="outlined"
            onClick={handleReset}
            disabled={loading}
            sx={{
              color: '#d32f2f',
              borderColor: '#d32f2f',
              mr: 1,
              '&:hover': { borderColor: '#b71c1c', backgroundColor: 'rgba(211, 47, 47, 0.04)' },
            }}
          >
            <span style={{ fontWeight: 'bold' }}>重置</span>
          </Button>
          <Button
            variant={autoRefresh ? 'contained' : 'outlined'}
            color={autoRefresh ? 'error' : 'success'}
            onClick={toggleAutoRefresh}
            disabled={loading}
            startIcon={autoRefresh ? <PauseCircleOutlineIcon /> : <AutorenewIcon />}
            sx={{
              mr: 1,
              fontWeight: 'bold',
              minWidth: '140px',
              ...(autoRefresh && {
                backgroundColor: '#d32f2f',
                boxShadow: '0 2px 4px rgba(211,47,47,0.3)',
                '&:hover': {
                  backgroundColor: '#b71c1c',
                },
              }),
              ...(!autoRefresh && {
                borderWidth: '2px',
                backgroundColor: 'rgba(0,200,83,0.08)',
                borderColor: '#00c853',
                color: '#00c853',
                '&:hover': {
                  backgroundColor: 'rgba(0,200,83,0.16)',
                  borderWidth: '2px',
                  borderColor: '#00a839',
                },
              }),
            }}
          >
            {autoRefresh ? (
              <span style={{ color: '#ffffff', fontWeight: 'bold' }}>停止刷新</span>
            ) : (
              <span style={{ fontWeight: 'bold' }}>开启自动刷新</span>
            )}
          </Button>
          {autoRefresh && (
            <Box sx={{ display: 'flex', alignItems: 'center', ml: 1, mt: { xs: 1, sm: 0 } }}>
              <Typography variant="body2" sx={{ mr: 1 }}>
                刷新间隔：
              </Typography>
              <TextField
                size="small"
                type="number"
                value={refreshInterval}
                onChange={handleRefreshIntervalChange}
                inputProps={{ min: 5, max: 300, step: 5 }}
                sx={{ width: '80px', mr: 1 }}
              />
              <Typography variant="body2" sx={{ mr: 2 }}>
                秒
              </Typography>
              {lastRefreshTime && (
                <Typography
                  variant="body2"
                  sx={{
                    color: '#00c853',
                    fontWeight: 'bold',
                    backgroundColor: 'rgba(0, 200, 83, 0.12)',
                    padding: '3px 10px',
                    borderRadius: '4px',
                    border: '1px solid rgba(0, 200, 83, 0.3)',
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <AutorenewIcon
                    fontSize="small"
                    sx={{ mr: 0.5, animation: autoRefresh ? 'spin 2s linear infinite' : 'none' }}
                  />
                  上次刷新: {lastRefreshTime}
                </Typography>
              )}
            </Box>
          )}
        </Box>
      </Paper>

      {/* 错误信息显示 */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* 信息提示 */}
      {showInfo && (
        <Alert severity="info" icon={<InfoIcon />} sx={{ mb: 2 }}>
          {selectedTab === 0
            ? '请输入年份和月份，点击“开始检查”按钮获取所有数据看板。'
            : selectedTab === 1
              ? '请输入年份和月份，点击“开始检查”按钮开始检查新增表数据的一致性。'
              : selectedTab === 5
                ? '请输入年份和月份，点击“开始检查”按钮开始检查债务变动的一致性。'
                : '请输入年份和月份，点击“开始检查”按钮开始检查处置表数据的一致性。'}
        </Alert>
      )}

      {/* 结果显示区域 */}
      {!showInfo && !loading && !error && (
        <>
          {selectedTab === 0 && renderDataSummaryResult()}
          {selectedTab === 1 && renderAddTableResult()}
          {selectedTab === 2 && renderDisposalTableResult()}
          {selectedTab === 3 && renderAddAmountCheckResult()}
          {selectedTab === 4 && renderDisposalAmountCheckResult()}
          {selectedTab === 5 && renderDebtChangeConsistencyResult()}
        </>
      )}
    </Box>
  );
};

export default DataConsistencyCheck;
