import { useState, useEffect, useCallback } from 'react';
import GenericDataTable from 'components/tables/GenericDataTable';
import { Card } from '@mui/material';
import MDBox from 'components/MDBox';
import MDTypography from 'components/MDTypography';
import MDButton from 'components/MDButton';
import DashboardLayout from 'examples/LayoutContainers/DashboardLayout';
import DashboardNavbar from 'examples/Navbars/DashboardNavbar';
import { useAuth } from 'context/AuthContext';
import api from 'utils/api';

function UserManagement() {
  // 获取用户状态对应的标签
  // 新增加内容
  const getStatusLabel = status => {
    const statusMap = {
      PENDING: '待审核',
      ACTIVE: '已激活',
      DISABLED: '已禁用',
    };
    return statusMap[status] || status;
  };
  const { user } = useAuth();
  const [users, setUsers] = useState([]);
  const [, setCompanies] = useState([]);
  const [, setUserPermissions] = useState([]);
  const [, setSelectedUser] = useState(null);
  const [, setPermissionDialogOpen] = useState(false);
  const [, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [, setConfirmDeleteOpen] = useState(false);
  const [, setUserToDelete] = useState(null);

  // 检查用户是否有管理员权限
  useEffect(() => {
    if (user?.role !== 'ROLE_ADMIN') {
      setError('您没有访问此页面的权限');
    }
  }, [user]);

  const fetchUsers = useCallback(async () => {
    setLoading(true);
    try {
      const response = await api.get('/users');
      setUsers(response.data);
    } catch (err) {
      console.error('获取用户列表失败:', err);
      setError(err.response?.data?.message || err.message || '获取用户列表失败');
    } finally {
      setLoading(false);
    }
  }, [setError]);

  const fetchCompanies = useCallback(async () => {
    try {
      const response = await api.get('/companies');
      setCompanies(response.data?.data || []);
    } catch (error) {
      console.error('获取公司列表失败:', error);
      setError(error.response?.data?.message || error.message || '获取公司列表失败');
    }
  }, [setError]);


  useEffect(() => {
    if (user && user.token) {
      fetchUsers();
      fetchCompanies();
    }
  }, [user, fetchUsers, fetchCompanies]);

  const handleUpdateStatus = async (userId, newStatus) => {
    try {
      await api.put(`/users/${userId}/status`, { status: newStatus });
      // 刷新用户列表
      fetchUsers();
    } catch (err) {
      console.error('更新用户状态失败:', err);
      setError(err.response?.data?.message || err.message || '更新用户状态失败');
    }
  };

  // 如果没有管理员权限，显示错误信息
  if (user?.role !== 'ROLE_ADMIN') {
    return (
      <DashboardLayout>
        <DashboardNavbar />
        <MDBox pt={6} pb={3}>
          <MDTypography variant="h4" color="error">
            {error || '您没有访问此页面的权限'}
          </MDTypography>
        </MDBox>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <DashboardNavbar />
      {/* GenericDataTable版本的用户管理表格 */}
      <MDBox pt={4} pb={3}>
        <Card>
          <MDBox p={3}>
            <MDTypography variant="h6" color="text">
              用户管理
            </MDTypography>
          </MDBox>
          <MDBox p={3}>
            <GenericDataTable
              columns={[
                { field: 'username', headerName: '用户名', width: '12%' },
                { field: 'name', headerName: '姓名', width: '10%' },
                { field: 'company', headerName: '公司', width: '12%' },
                { field: 'department', headerName: '部门', width: '10%' },
                { field: 'email', headerName: '邮箱', width: '15%' },
                { field: 'phone', headerName: '手机', width: '10%' },
                { field: 'position', headerName: '职位', width: '10%' },
                { field: 'status', headerName: '状态', width: '8%' },
                { field: 'role', headerName: '角色', width: '8%' },
              ]}
              data={users.map(user => ({
                ...user,
                status: getStatusLabel(user.status),
              }))}
              renderActions={user => {
                // 如果用户状态是待审核，显示通过和拒绝按钮
                if (user.status === '待审核') {
                  return (
                    <div style={{ display: 'flex', flexDirection: 'row', gap: '4px' }}>
                      <MDButton
                        variant="outlined"
                        color="success"
                        size="small"
                        sx={{ fontSize: '12px', padding: '4px 8px', minWidth: '50px' }}
                        onClick={() => handleUpdateStatus(user.id, 'ACTIVE')}
                      >
                        通过
                      </MDButton>
                      <MDButton
                        variant="outlined"
                        color="error"
                        size="small"
                        sx={{ fontSize: '12px', padding: '4px 8px', minWidth: '50px' }}
                        onClick={() => handleUpdateStatus(user.id, 'DISABLED')}
                      >
                        拒绝
                      </MDButton>
                    </div>
                  );
                }
                // 其他状态显示修改权限按钮
                return (
                  <div style={{ display: 'flex', flexDirection: 'row', gap: '4px' }}>
                    <MDButton
                      variant="outlined"
                      color="info"
                      size="small"
                      sx={{ fontSize: '12px', padding: '4px 8px', minWidth: '50px' }}
                      onClick={() => {
                        setSelectedUser(user);
                        setPermissionDialogOpen(true);
                      }}
                    >
                      权限
                    </MDButton>
                    <MDButton
                      variant="outlined"
                      color="error"
                      size="small"
                      sx={{ fontSize: '12px', padding: '4px 8px', minWidth: '50px' }}
                      onClick={() => {
                        setUserToDelete(user);
                        setConfirmDeleteOpen(true);
                      }}
                    >
                      删除
                    </MDButton>
                  </div>
                );
              }}
            />
          </MDBox>
        </Card>
      </MDBox>
    </DashboardLayout>
  );
}

export default UserManagement;
