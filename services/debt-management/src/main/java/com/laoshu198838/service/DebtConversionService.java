package com.laoshu198838.service;

import com.laoshu198838.dto.debt.DebtConversionRequestDTO;
import com.laoshu198838.dto.debt.DebtConversionResponseDTO;
import com.laoshu198838.dto.debt.DebtSearchResultDTO;
import com.laoshu198838.entity.overdue_debt.LitigationClaim;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve;
import com.laoshu198838.entity.overdue_debt.LitigationClaim.LitigationCompositeKey;
import com.laoshu198838.entity.overdue_debt.NonLitigationClaim.NonLitigationCompositeKey;
import com.laoshu198838.entity.overdue_debt.ImpairmentReserve.ImpairmentReserveKey;
import com.laoshu198838.repository.overdue_debt.LitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.NonLitigationClaimRepository;
import com.laoshu198838.repository.overdue_debt.ImpairmentReserveRepository;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 债权转换服务类
 * 实现诉讼与非诉讼债权相互转换的核心业务逻辑
 * 
 * <AUTHOR>
 */
@Service
public class DebtConversionService {
    
    private static final Logger logger = LoggerFactory.getLogger(DebtConversionService.class);
    
    @Autowired
    private LitigationClaimRepository litigationClaimRepository;
    
    @Autowired
    private NonLitigationClaimRepository nonLitigationClaimRepository;
    
    @Autowired
    private ImpairmentReserveRepository impairmentReserveRepository;
    
    /**
     * 搜索可转换的债权记录
     * 
     * @param creditor 债权人（可选）
     * @param debtor 债务人（可选）
     * @return 匹配的债权记录列表
     */
    public List<DebtSearchResultDTO> searchConvertibleDebts(String creditor, String debtor) {
        logger.info("开始搜索可转换债权记录，债权人：{}，债务人：{}", creditor, debtor);
        
        List<DebtSearchResultDTO> results = new ArrayList<>();
        
        try {
            // 搜索诉讼债权
            List<LitigationClaim> litigationClaims = findLitigationClaims(creditor, debtor);
            for (LitigationClaim claim : litigationClaims) {
                DebtSearchResultDTO dto = convertLitigationToSearchResult(claim);
                results.add(dto);
            }
            
            // 搜索非诉讼债权
            List<NonLitigationClaim> nonLitigationClaims = findNonLitigationClaims(creditor, debtor);
            for (NonLitigationClaim claim : nonLitigationClaims) {
                DebtSearchResultDTO dto = convertNonLitigationToSearchResult(claim);
                results.add(dto);
            }
            
            logger.info("搜索完成，找到 {} 条可转换记录", results.size());
            
        } catch (Exception e) {
            logger.error("搜索可转换债权记录时发生错误", e);
            throw new RuntimeException("搜索债权记录失败：" + e.getMessage(), e);
        }
        
        return results;
    }
    
    /**
     * 诉讼转非诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @Transactional
    public DebtConversionResponseDTO convertLitigationToNonLitigation(DebtConversionRequestDTO request) {
        logger.info("开始执行诉讼转非诉讼，债权人：{}，债务人：{}", request.getCreditor(), request.getDebtor());
        
        try {
            // 1. 查找诉讼表记录
            LitigationCompositeKey litigationKey = new LitigationCompositeKey();
            litigationKey.setCreditor(request.getCreditor());
            litigationKey.setDebtor(request.getDebtor());
            litigationKey.setPeriod(request.getPeriod());
            litigationKey.setYear(request.getYear());
            litigationKey.setMonth(request.getMonth());
            
            LitigationClaim litigationClaim = litigationClaimRepository.findById(litigationKey)
                .orElseThrow(() -> new RuntimeException("未找到对应的诉讼债权记录"));
            
            // 2. 保存原始数据用于转换
            BigDecimal originalBalance = litigationClaim.getLastMonthDebtBalance();
            
            // 3. 更新诉讼表（清零相关字段）
            litigationClaim.setLitigationPrincipal(BigDecimal.ZERO);
            litigationClaim.setLitigationInterest(BigDecimal.ZERO);
            litigationClaim.setCurrentMonthDebtBalance(BigDecimal.ZERO);
            
            String originalRemark = litigationClaim.getRemark() != null ? litigationClaim.getRemark() : "";
            String conversionRemark = String.format("【转换】%d年%d月转为非诉讼。%s", 
                request.getConversionYear(), request.getConversionMonth(), 
                request.getRemark() != null ? request.getRemark() : "");
            litigationClaim.setRemark(originalRemark.isEmpty() ? conversionRemark : originalRemark + "; " + conversionRemark);
            
            litigationClaimRepository.save(litigationClaim);
            
            // 4. 创建非诉讼表记录
            createNonLitigationRecord(request, litigationClaim, originalBalance);
            
            // 5. 更新减值准备表
            updateImpairmentReserveForConversion(request.getCreditor(), request.getDebtor(), 
                request.getConversionYear(), request.getConversionMonth(), request.getPeriod(), 
                "是", "否");
            
            logger.info("诉讼转非诉讼执行成功");
            
            return DebtConversionResponseDTO.success("诉讼债权已成功转换为非诉讼债权", 
                "litigation_to_non_litigation", 1);
                
        } catch (Exception e) {
            logger.error("诉讼转非诉讼执行失败", e);
            throw new RuntimeException("诉讼转非诉讼失败：" + e.getMessage(), e);
        }
    }
    
    /**
     * 非诉讼转诉讼
     * 
     * @param request 转换请求数据
     * @return 转换结果
     */
    @Transactional
    public DebtConversionResponseDTO convertNonLitigationToLitigation(DebtConversionRequestDTO request) {
        logger.info("开始执行非诉讼转诉讼，债权人：{}，债务人：{}", request.getCreditor(), request.getDebtor());
        
        try {
            // 1. 查找非诉讼表记录
            NonLitigationCompositeKey nonLitigationKey = new NonLitigationCompositeKey();
            nonLitigationKey.setCreditor(request.getCreditor());
            nonLitigationKey.setDebtor(request.getDebtor());
            nonLitigationKey.setPeriod(request.getPeriod());
            nonLitigationKey.setYear(request.getYear());
            nonLitigationKey.setMonth(request.getMonth());
            
            NonLitigationClaim nonLitigationClaim = nonLitigationClaimRepository.findById(nonLitigationKey)
                .orElseThrow(() -> new RuntimeException("未找到对应的非诉讼债权记录"));
            
            // 2. 保存原始数据用于转换
            BigDecimal originalPrincipal = nonLitigationClaim.getCurrentMonthPrincipal();
            BigDecimal originalInterest = nonLitigationClaim.getCurrentMonthInterest();
            BigDecimal originalPenalty = nonLitigationClaim.getCurrentMonthPenalty();
            BigDecimal totalBalance = (originalPrincipal != null ? originalPrincipal : BigDecimal.ZERO)
                .add(originalInterest != null ? originalInterest : BigDecimal.ZERO)
                .add(originalPenalty != null ? originalPenalty : BigDecimal.ZERO);
            
            // 3. 更新非诉讼表（清零相关字段）
            nonLitigationClaim.setCurrentMonthPrincipal(BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthInterest(BigDecimal.ZERO);
            nonLitigationClaim.setCurrentMonthPenalty(BigDecimal.ZERO);
            
            String originalRemark = nonLitigationClaim.getRemark() != null ? nonLitigationClaim.getRemark() : "";
            String conversionRemark = String.format("【转换】%d年%d月转为诉讼。%s", 
                request.getConversionYear(), request.getConversionMonth(), 
                request.getRemark() != null ? request.getRemark() : "");
            nonLitigationClaim.setRemark(originalRemark.isEmpty() ? conversionRemark : originalRemark + "; " + conversionRemark);
            
            nonLitigationClaimRepository.save(nonLitigationClaim);
            
            // 4. 创建诉讼表记录
            createLitigationRecord(request, nonLitigationClaim, originalPrincipal, originalInterest, originalPenalty, totalBalance);
            
            // 5. 更新减值准备表
            updateImpairmentReserveForConversion(request.getCreditor(), request.getDebtor(), 
                request.getConversionYear(), request.getConversionMonth(), request.getPeriod(), 
                "否", "是");
            
            logger.info("非诉讼转诉讼执行成功");
            
            return DebtConversionResponseDTO.success("非诉讼债权已成功转换为诉讼债权", 
                "non_litigation_to_litigation", 1);
                
        } catch (Exception e) {
            logger.error("非诉讼转诉讼执行失败", e);
            throw new RuntimeException("非诉讼转诉讼失败：" + e.getMessage(), e);
        }
    }
    
    // === 私有辅助方法 ===
    
    private List<LitigationClaim> findLitigationClaims(String creditor, String debtor) {
        if (creditor != null && debtor != null) {
            return litigationClaimRepository.findByCreditorAndDebtor(creditor, debtor);
        } else if (creditor != null) {
            return litigationClaimRepository.findByCreditor(creditor);
        } else if (debtor != null) {
            return litigationClaimRepository.findByDebtor(debtor);
        } else {
            return litigationClaimRepository.findAll();
        }
    }
    
    private List<NonLitigationClaim> findNonLitigationClaims(String creditor, String debtor) {
        if (creditor != null && debtor != null) {
            return nonLitigationClaimRepository.findByCreditorAndDebtor(creditor, debtor);
        } else if (creditor != null) {
            return nonLitigationClaimRepository.findByCreditor(creditor);
        } else if (debtor != null) {
            return nonLitigationClaimRepository.findByDebtor(debtor);
        } else {
            return nonLitigationClaimRepository.findAll();
        }
    }
    
    private DebtSearchResultDTO convertLitigationToSearchResult(LitigationClaim claim) {
        return DebtSearchResultDTO.builder()
            .creditor(claim.getId().getCreditor())
            .debtor(claim.getId().getDebtor())
            .period(claim.getId().getPeriod())
            .debtBalance(claim.getCurrentMonthDebtBalance())
            .currentStatus("诉讼")
            .isLitigation("是")
            .managementCompany(claim.getManagementCompany())
            .subjectName(claim.getSubjectName())
            .year(claim.getId().getYear())
            .month(claim.getId().getMonth())
            .sourceTable("litigation")
            .litigationCase(claim.getLitigationCase())
            .build();
    }
    
    private DebtSearchResultDTO convertNonLitigationToSearchResult(NonLitigationClaim claim) {
        BigDecimal totalBalance = (claim.getCurrentMonthPrincipal() != null ? claim.getCurrentMonthPrincipal() : BigDecimal.ZERO)
            .add(claim.getCurrentMonthInterest() != null ? claim.getCurrentMonthInterest() : BigDecimal.ZERO)
            .add(claim.getCurrentMonthPenalty() != null ? claim.getCurrentMonthPenalty() : BigDecimal.ZERO);
            
        return DebtSearchResultDTO.builder()
            .creditor(claim.getId().getCreditor())
            .debtor(claim.getId().getDebtor())
            .period(claim.getId().getPeriod())
            .debtBalance(totalBalance)
            .currentStatus("非诉讼")
            .isLitigation("否")
            .managementCompany(claim.getManagementCompany())
            .subjectName(claim.getSubjectName())
            .year(claim.getId().getYear())
            .month(claim.getId().getMonth())
            .sourceTable("non_litigation")
            .principal(claim.getCurrentMonthPrincipal())
            .interest(claim.getCurrentMonthInterest())
            .penalty(claim.getCurrentMonthPenalty())
            .build();
    }
    
    private void createNonLitigationRecord(DebtConversionRequestDTO request, LitigationClaim sourceClaim, BigDecimal originalBalance) {
        NonLitigationClaim newClaim = new NonLitigationClaim();
        
        // 设置复合主键
        NonLitigationCompositeKey key = new NonLitigationCompositeKey();
        key.setCreditor(request.getCreditor());
        key.setDebtor(request.getDebtor());
        key.setPeriod(request.getPeriod());
        key.setYear(request.getConversionYear());
        key.setMonth(request.getConversionMonth());
        newClaim.setId(key);
        
        // 设置基本信息
        // 序号字段由数据库自动生成，无需手动设置
        newClaim.setSubjectName(sourceClaim.getSubjectName());
        newClaim.setManagementCompany(sourceClaim.getManagementCompany());
        newClaim.setResponsiblePerson(sourceClaim.getResponsiblePerson());
        
        // 设置金额信息
        newClaim.setLastMonthPrincipal(BigDecimal.ZERO);
        newClaim.setLastMonthInterest(BigDecimal.ZERO);
        newClaim.setLastMonthPenalty(BigDecimal.ZERO);
        newClaim.setCurrentMonthPrincipalIncreaseDecrease(originalBalance);
        newClaim.setCurrentMonthInterestIncreaseDecrease(BigDecimal.ZERO);
        newClaim.setCurrentMonthPenaltyIncreaseDecrease(BigDecimal.ZERO);
        newClaim.setCurrentMonthPrincipal(originalBalance);
        newClaim.setCurrentMonthInterest(BigDecimal.ZERO);
        newClaim.setCurrentMonthPenalty(BigDecimal.ZERO);
        
        // 设置其他信息
        newClaim.setAnnualRecoveryTarget(sourceClaim.getAnnualRecoveryTarget() != null ? 
            new BigDecimal(sourceClaim.getAnnualRecoveryTarget()) : BigDecimal.ZERO);
        newClaim.setAnnualCumulativeRecovery(sourceClaim.getAnnualCumulativeRecovery());
        newClaim.setArrangement(sourceClaim.getArrangement());
        
        String conversionRemark = String.format("【转换】从诉讼转换而来（%d年%d月）。%s", 
            request.getConversionYear(), request.getConversionMonth(), 
            request.getRemark() != null ? request.getRemark() : "");
        newClaim.setRemark(conversionRemark);
        
        nonLitigationClaimRepository.save(newClaim);
    }
    
    private void createLitigationRecord(DebtConversionRequestDTO request, NonLitigationClaim sourceClaim, 
            BigDecimal originalPrincipal, BigDecimal originalInterest, BigDecimal originalPenalty, BigDecimal totalBalance) {
        LitigationClaim newClaim = new LitigationClaim();
        
        // 设置复合主键
        LitigationCompositeKey key = new LitigationCompositeKey();
        key.setCreditor(request.getCreditor());
        key.setDebtor(request.getDebtor());
        key.setPeriod(request.getPeriod());
        key.setYear(request.getConversionYear());
        key.setMonth(request.getConversionMonth());
        newClaim.setId(key);
        
        // 设置基本信息
        // 序号字段由数据库自动生成，无需手动设置
        newClaim.setLitigationCase(request.getLitigationCase());
        newClaim.setSubjectName(sourceClaim.getSubjectName());
        newClaim.setManagementCompany(sourceClaim.getManagementCompany());
        newClaim.setResponsiblePerson(sourceClaim.getResponsiblePerson());
        
        // 设置债权金额
        newClaim.setLastMonthDebtBalance(totalBalance);
        newClaim.setLitigationPrincipal(originalPrincipal != null ? originalPrincipal : BigDecimal.ZERO);
        newClaim.setLitigationInterest((originalInterest != null ? originalInterest : BigDecimal.ZERO)
            .add(originalPenalty != null ? originalPenalty : BigDecimal.ZERO));
        newClaim.setCurrentMonthDebtBalance(totalBalance);
        
        // 设置诉讼特有字段
        newClaim.setLitigationOccurredPrincipal(request.getLitigationOccurredPrincipal());
        newClaim.setLitigationInterestFee(request.getLitigationInterestFee());
        newClaim.setLitigationFee(request.getLitigationFee());
        newClaim.setIntermediaryFee(request.getIntermediaryFee());
        
        // 设置其他信息
        newClaim.setAnnualRecoveryTarget(sourceClaim.getAnnualRecoveryTarget() != null ? 
            sourceClaim.getAnnualRecoveryTarget().toString() : "0");
        newClaim.setAnnualCumulativeRecovery(sourceClaim.getAnnualCumulativeRecovery());
        newClaim.setArrangement(sourceClaim.getArrangement());
        
        String conversionRemark = String.format("【转换】从非诉讼转换而来（%d年%d月）。%s", 
            request.getConversionYear(), request.getConversionMonth(), 
            request.getRemark() != null ? request.getRemark() : "");
        newClaim.setRemark(conversionRemark);
        
        litigationClaimRepository.save(newClaim);
    }
    
    private void updateImpairmentReserveForConversion(String creditor, String debtor, Integer year, Integer month, 
            String period, String fromLitigation, String toLitigation) {
        try {
            // 查找减值准备记录
            ImpairmentReserveKey key = new ImpairmentReserveKey();
            key.setCreditor(creditor);
            key.setDebtor(debtor);
            key.setYear(year);
            key.setMonth(month);
            key.setPeriod(period);
            key.setIsLitigation(fromLitigation);
            
            Optional<ImpairmentReserve> reserveOpt = impairmentReserveRepository.findById(key);
            if (reserveOpt.isPresent()) {
                ImpairmentReserve reserve = reserveOpt.get();
                
                // 更新是否涉诉状态
                key.setIsLitigation(toLitigation);
                reserve.setId(key);
                
                impairmentReserveRepository.save(reserve);
                logger.info("减值准备表更新成功，是否涉诉从 {} 更新为 {}", fromLitigation, toLitigation);
            } else {
                logger.warn("未找到对应的减值准备记录，无法更新是否涉诉状态");
            }
        } catch (Exception e) {
            logger.error("更新减值准备表时发生错误", e);
            // 不抛出异常，避免影响主要转换流程
        }
    }
}